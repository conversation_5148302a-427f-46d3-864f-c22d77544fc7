using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using ProManage.Forms.ChildForms;
using ProManage.Modules.Helpers.PermissionManagementForm;
using ProManage.Modules.Models.PermissionManagementForm;
using ProManage.Modules.Services;

namespace ProManage.Forms
{
    /// <summary>
    /// Comprehensive permission management form with 3 tabs for role permissions,
    /// user permissions, and global user management permissions.
    /// </summary>
    public partial class PermissionManagementForm : XtraForm
    {
        #region Private Fields

        private readonly PermissionGridHelper _gridHelper;
        private bool _isLoading = false;
        private int _currentRoleId = 0;
        private int _currentUserId = 0;
        private bool _hasUnsavedChanges = false;

        #endregion

        #region Constructor

        public PermissionManagementForm()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("Starting PermissionManagementForm constructor");
                InitializeComponent();
                System.Diagnostics.Debug.WriteLine("InitializeComponent completed");

                _gridHelper = new PermissionGridHelper();
                System.Diagnostics.Debug.WriteLine("PermissionGridHelper created successfully");

                System.Diagnostics.Debug.WriteLine("PermissionManagementForm constructor completed successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error in PermissionManagementForm constructor: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        #endregion

        #region Form Initialization

        /// <summary>
        /// Handles the form load event - follows the same pattern as DatabaseForm and ParametersForm
        /// </summary>
        private void PermissionManagementForm_Load(object sender, EventArgs e)
        {
            try
            {
                // Initialize the form properly
                InitializeForm();
                Debug.WriteLine("PermissionManagementForm loaded successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading Permission Management Form: {ex.Message}");
                MessageBox.Show($"Error loading Permission Management Form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Initialize form components and setup
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                Debug.WriteLine("Starting InitializeForm");

                // Test database connection first
                Debug.WriteLine("Testing database connection");
                TestDatabaseConnection();
                Debug.WriteLine("Database connection test completed");

                // Set form properties for MDI
                Debug.WriteLine("Setting MDI parent");
                var mdiParent = System.Windows.Forms.Application.OpenForms.OfType<Form>().FirstOrDefault(f => f.IsMdiContainer);
                if (mdiParent != null)
                    this.MdiParent = mdiParent;
                this.WindowState = FormWindowState.Maximized;
                Debug.WriteLine("MDI parent set successfully");

                // Initialize grids
                Debug.WriteLine("Initializing grids");
                InitializeGrids();
                Debug.WriteLine("Grids initialized successfully");

                // Load initial data
                Debug.WriteLine("Loading roles");
                LoadRoles();
                Debug.WriteLine("Roles loaded successfully");

                Debug.WriteLine("Loading users");
                LoadUsers();
                Debug.WriteLine("Users loaded successfully");

                // Setup event handlers
                Debug.WriteLine("Setting up event handlers");
                SetupEventHandlers();
                Debug.WriteLine("Event handlers set up successfully");

                Debug.WriteLine("PermissionManagementForm initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in InitializeForm: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error initializing Permission Management Form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                // Don't throw - allow form to show even with initialization errors
            }
        }

        /// <summary>
        /// Test database connection and schema
        /// </summary>
        private void TestDatabaseConnection()
        {
            try
            {
                Debug.WriteLine("Testing database connection and schema");

                // Test basic connection
                var connectionManager = ProManage.Modules.Connections.DatabaseConnectionManager.Instance;
                if (!connectionManager.IsConnected)
                {
                    Debug.WriteLine("Database is not connected, attempting to connect");
                    if (!connectionManager.OpenConnection())
                    {
                        Debug.WriteLine($"Database connection failed: {connectionManager.LastError}");
                        // Don't throw exception for database connection issues during form initialization
                        // Just log the error and continue with form initialization
                        return;
                    }
                }
                Debug.WriteLine("Database connection is active");

                // Verify RBAC schema exists
                Debug.WriteLine("Verifying RBAC schema");
                if (!ProManage.Modules.Connections.PermissionDatabaseService.VerifyAndCreateSchema())
                {
                    Debug.WriteLine("Failed to verify or create RBAC database schema");
                    // Don't throw exception for schema issues during form initialization
                    // Just log the error and continue with form initialization
                    return;
                }
                Debug.WriteLine("RBAC schema verified successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Database connection test failed: {ex.Message}");
                // Don't throw exception for database issues during form initialization
                // Just log the error and continue with form initialization
            }
        }

        /// <summary>
        /// Initialize all grids with proper configuration
        /// </summary>
        private void InitializeGrids()
        {
            try
            {
                Debug.WriteLine("Starting InitializeGrids");

                // Check if _gridHelper is null
                if (_gridHelper == null)
                {
                    throw new InvalidOperationException("_gridHelper is null");
                }
                Debug.WriteLine("_gridHelper is not null");

                // Check if grid controls exist
                if (gridControlRolePermissions == null)
                {
                    throw new InvalidOperationException("gridControlRolePermissions is null");
                }
                if (gridViewRolePermissions == null)
                {
                    throw new InvalidOperationException("gridViewRolePermissions is null");
                }
                Debug.WriteLine("Role permission grid controls are not null");

                // Configure role permissions grid
                Debug.WriteLine("Configuring role permissions grid");
                _gridHelper.ConfigureRolePermissionsGrid(gridControlRolePermissions, gridViewRolePermissions);
                gridViewRolePermissions.OptionsBehavior.Editable = false; // Start in read-only mode
                Debug.WriteLine("Role permissions grid configured");

                // Check user permission grid controls
                if (gridControlUserPermissions == null)
                {
                    throw new InvalidOperationException("gridControlUserPermissions is null");
                }
                if (gridViewUserPermissions == null)
                {
                    throw new InvalidOperationException("gridViewUserPermissions is null");
                }
                Debug.WriteLine("User permission grid controls are not null");

                // Configure user permissions grid
                Debug.WriteLine("Configuring user permissions grid");
                _gridHelper.ConfigureUserPermissionsGrid(gridControlUserPermissions, gridViewUserPermissions);
                gridViewUserPermissions.OptionsBehavior.Editable = false; // Start in read-only mode
                Debug.WriteLine("User permissions grid configured");

                // Check global permission checkboxes
                if (chkCanCreateNewEntry == null || chkCanEditUsers == null || chkCanDeleteUsers == null || chkCanPrintUsers == null)
                {
                    throw new InvalidOperationException("One or more global permission checkboxes are null");
                }
                Debug.WriteLine("Global permission checkboxes are not null");

                // Set global permissions checkboxes to read-only initially
                chkCanCreateNewEntry.Enabled = false;
                chkCanEditUsers.Enabled = false;
                chkCanDeleteUsers.Enabled = false;
                chkCanPrintUsers.Enabled = false;

                Debug.WriteLine("Grids initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing grids: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        /// <summary>
        /// Setup event handlers for form controls
        /// </summary>
        private void SetupEventHandlers()
        {
            try
            {
                // Role selection events
                cmbRoles.SelectedIndexChanged += CmbRoles_SelectedIndexChanged;

                // User selection events
                cmbUsers.SelectedIndexChanged += CmbUsers_SelectedIndexChanged;
                cmbUsersGlobal.SelectedIndexChanged += CmbUsersGlobal_SelectedIndexChanged;

                // Tab change events
                tabControlMain.SelectedPageChanged += TabControlMain_SelectedPageChanged;

                // Grid events for tracking changes
                gridViewRolePermissions.CellValueChanged += GridViewRolePermissions_CellValueChanged;
                gridViewUserPermissions.CellValueChanged += GridViewUserPermissions_CellValueChanged;

                // Global permission checkbox events
                chkCanCreateNewEntry.CheckedChanged += GlobalPermission_CheckedChanged;
                chkCanEditUsers.CheckedChanged += GlobalPermission_CheckedChanged;
                chkCanDeleteUsers.CheckedChanged += GlobalPermission_CheckedChanged;
                chkCanPrintUsers.CheckedChanged += GlobalPermission_CheckedChanged;

                // Button events
                btnSave.Click += BtnSave_Click;
                btnCancel.Click += BtnCancel_Click;
                btnRefresh.Click += BtnRefresh_Click;

                btnAddRole.Click += BtnAddRole_Click;

                // Edit/Load button events
                btnRoleEdit.Click += BtnRoleEdit_Click;
                btnRoleLoad.Click += BtnRoleLoad_Click;
                btnDeleteRole.Click += BtnDeleteRole_Click;
                btnUserEdit.Click += BtnUserEdit_Click;
                btnUserLoad.Click += BtnUserLoad_Click;
                btnGlobalEdit.Click += BtnGlobalEdit_Click;
                btnGlobalLoad.Click += BtnGlobalLoad_Click;

                Debug.WriteLine("Event handlers setup successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up event handlers: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Data Loading

        /// <summary>
        /// Load roles into dropdown
        /// </summary>
        private void LoadRoles()
        {
            try
            {
                Debug.WriteLine("Starting LoadRoles");
                _isLoading = true;

                // Check if cmbRoles exists
                if (cmbRoles == null)
                {
                    throw new InvalidOperationException("cmbRoles is null");
                }
                Debug.WriteLine("cmbRoles is not null");

                Debug.WriteLine("Calling PermissionService.GetAllRoles()");
                var roles = PermissionService.GetAllRoles();

                if (roles == null)
                {
                    Debug.WriteLine("PermissionService.GetAllRoles() returned null - using empty list");
                    roles = new List<ProManage.Modules.Models.PermissionManagementForm.Role>();
                }
                Debug.WriteLine($"PermissionService.GetAllRoles() returned {roles.Count} roles");

                cmbRoles.Properties.Items.Clear();
                cmbRoles.Properties.Items.AddRange(roles.Select(r => $"{r.RoleId}|{r.RoleName}").ToArray());

                if (roles.Any())
                {
                    cmbRoles.SelectedIndex = 0;
                }

                Debug.WriteLine($"Loaded {roles.Count} roles successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in LoadRoles: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                // Remove MessageBox to avoid confirmation dialog
                // Don't throw - allow form to continue loading
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// Load users into dropdown
        /// </summary>
        private void LoadUsers()
        {
            try
            {
                Debug.WriteLine("Starting LoadUsers");
                _isLoading = true;

                // Check if user combo boxes exist
                if (cmbUsers == null)
                {
                    throw new InvalidOperationException("cmbUsers is null");
                }
                if (cmbUsersGlobal == null)
                {
                    throw new InvalidOperationException("cmbUsersGlobal is null");
                }
                Debug.WriteLine("User combo boxes are not null");

                Debug.WriteLine("Calling PermissionService.GetAllUsers()");
                var users = PermissionService.GetAllUsers();

                if (users == null)
                {
                    Debug.WriteLine("PermissionService.GetAllUsers() returned null - using empty list");
                    users = new List<ProManage.Modules.Models.PermissionManagementForm.UserInfo>();
                }
                Debug.WriteLine($"PermissionService.GetAllUsers() returned {users.Count} users");

                cmbUsers.Properties.Items.Clear();
                cmbUsers.Properties.Items.AddRange(users.Select(u => $"{u.UserId}|{u.Username} - {u.FullName}").ToArray());

                // Also populate global permissions user combo
                cmbUsersGlobal.Properties.Items.Clear();
                cmbUsersGlobal.Properties.Items.AddRange(users.Select(u => $"{u.UserId}|{u.Username} - {u.FullName}").ToArray());

                if (users.Any())
                {
                    cmbUsers.SelectedIndex = 0;
                    cmbUsersGlobal.SelectedIndex = 0;
                }

                Debug.WriteLine($"Loaded {users.Count} users successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in LoadUsers: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                MessageBox.Show($"Error loading users: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                // Don't throw - allow form to continue loading
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// Load role permissions for selected role
        /// </summary>
        private void LoadRolePermissions()
        {
            if (_currentRoleId <= 0) return;

            try
            {
                _isLoading = true;
                _gridHelper.LoadRolePermissions(gridControlRolePermissions, _currentRoleId);
                Debug.WriteLine($"Loaded permissions for role ID: {_currentRoleId}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading role permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// Load user permissions for selected user
        /// </summary>
        private void LoadUserPermissions()
        {
            if (_currentUserId <= 0) return;

            try
            {
                _isLoading = true;
                _gridHelper.LoadUserPermissions(gridControlUserPermissions, _currentUserId);
                LoadGlobalPermissions();
                Debug.WriteLine($"Loaded permissions for user ID: {_currentUserId}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading user permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// Load global permissions for selected user
        /// </summary>
        private void LoadGlobalPermissions()
        {
            if (_currentUserId <= 0) return;

            try
            {
                _isLoading = true;

                var globalPermissions = GlobalPermissionService.GetGlobalPermissions(_currentUserId);

                chkCanCreateNewEntry.Checked = globalPermissions?.CanCreateUsers ?? false;
                chkCanEditUsers.Checked = globalPermissions?.CanEditUsers ?? false;
                chkCanDeleteUsers.Checked = globalPermissions?.CanDeleteUsers ?? false;
                chkCanPrintUsers.Checked = globalPermissions?.CanPrintUsers ?? false;

                Debug.WriteLine($"Loaded global permissions for user ID: {_currentUserId}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading global permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle role selection change - NO AUTO-LOADING
        /// </summary>
        private void CmbRoles_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (_isLoading) return;

            try
            {
                if (cmbRoles.SelectedItem != null)
                {
                    var selectedValue = cmbRoles.SelectedItem.ToString();
                    var parts = selectedValue.Split('|');
                    if (parts.Length >= 2 && int.TryParse(parts[0], out int roleId))
                    {
                        _currentRoleId = roleId;
                        // DO NOT auto-load - user must click Load button
                        ClearRolePermissionsGrid();
                        _hasUnsavedChanges = false;
                        UpdateButtonStates();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error handling role selection: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle user selection change - NO AUTO-LOADING
        /// </summary>
        private void CmbUsers_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (_isLoading) return;

            try
            {
                if (cmbUsers.SelectedItem != null)
                {
                    var selectedValue = cmbUsers.SelectedItem.ToString();
                    var parts = selectedValue.Split('|');
                    if (parts.Length >= 2 && int.TryParse(parts[0], out int userId))
                    {
                        _currentUserId = userId;
                        // DO NOT auto-load - user must click Load button
                        ClearUserPermissionsGrid();
                        ClearGlobalPermissions();
                        _hasUnsavedChanges = false;
                        UpdateButtonStates();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error handling user selection: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle global permissions user selection change
        /// </summary>
        private void CmbUsersGlobal_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (_isLoading) return;

            try
            {
                if (cmbUsersGlobal.SelectedItem != null)
                {
                    var selectedValue = cmbUsersGlobal.SelectedItem.ToString();
                    var parts = selectedValue.Split('|');
                    if (parts.Length >= 2 && int.TryParse(parts[0], out int userId))
                    {
                        _currentUserId = userId;
                        LoadGlobalPermissions();
                        _hasUnsavedChanges = false;
                        UpdateButtonStates();

                        // Synchronize with user permissions combo
                        SynchronizeUserSelection(userId);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error handling global user selection: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle tab change to synchronize user selection
        /// </summary>
        private void TabControlMain_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            try
            {
                // Synchronize user selection between tabs
                if (e.Page == tabPageUserPermissions && _currentUserId > 0)
                {
                    LoadUserPermissions();
                }
                else if (e.Page == tabPageGlobalPermissions && _currentUserId > 0)
                {
                    LoadGlobalPermissions();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error handling tab change: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle role permissions grid cell value changes
        /// </summary>
        private void GridViewRolePermissions_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (!_isLoading)
            {
                _hasUnsavedChanges = true;
                UpdateButtonStates();
            }
        }

        /// <summary>
        /// Handle user permissions grid cell value changes
        /// </summary>
        private void GridViewUserPermissions_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (!_isLoading)
            {
                _hasUnsavedChanges = true;
                UpdateButtonStates();
                
                // Recalculate effective permissions if user override changed
                if (e.Column.FieldName.StartsWith("User"))
                {
                    RecalculateEffectivePermissions(e.RowHandle);
                }
            }
        }

        /// <summary>
        /// Handle global permission checkbox changes
        /// </summary>
        private void GlobalPermission_CheckedChanged(object sender, EventArgs e)
        {
            if (!_isLoading)
            {
                _hasUnsavedChanges = true;
                UpdateButtonStates();
            }
        }

        #endregion

        #region Button Event Handlers

        /// <summary>
        /// Handle Save button click
        /// </summary>
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (SaveChanges())
                {
                    _hasUnsavedChanges = false;
                    UpdateButtonStates();
                    MessageBox.Show("Changes saved successfully.", "Success",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Reset form after successful save
                    ResetForm();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving changes: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle Cancel button click
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                if (_hasUnsavedChanges)
                {
                    var result = MessageBox.Show("You have unsaved changes. Are you sure you want to cancel?",
                        "Confirm Cancel", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    
                    if (result == DialogResult.No)
                        return;
                }

                // Reload current data
                if (tabControlMain.SelectedTabPage == tabPageRolePermissions)
                {
                    LoadRolePermissions();
                }
                else if (tabControlMain.SelectedTabPage == tabPageUserPermissions)
                {
                    LoadUserPermissions();
                }
                else if (tabControlMain.SelectedTabPage == tabPageGlobalPermissions)
                {
                    LoadGlobalPermissions();
                }

                _hasUnsavedChanges = false;
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error canceling changes: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle Refresh button click
        /// </summary>
        private void BtnRefresh_Click(object sender, EventArgs e)
        {
            try
            {
                LoadRoles();
                LoadUsers();
                
                if (tabControlMain.SelectedTabPage == tabPageRolePermissions)
                {
                    LoadRolePermissions();
                }
                else if (tabControlMain.SelectedTabPage == tabPageUserPermissions)
                {
                    LoadUserPermissions();
                }
                // Global permissions removed - now handled in UserMasterForm

                _hasUnsavedChanges = false;
                UpdateButtonStates();
                
                MessageBox.Show("Data refreshed successfully.", "Success",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error refreshing data: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        /// <summary>
        /// Handle Add Role button click
        /// </summary>
        private void BtnAddRole_Click(object sender, EventArgs e)
        {
            try
            {
                // Removed debug message box
                Debug.WriteLine("Add Role button clicked");
                
                // Open AddRole form as MDI child
                var addRoleForm = new AddRole();
                Debug.WriteLine($"AddRole form instantiated: {addRoleForm != null}");
                
                if (this.MdiParent == null)
                {
                    Debug.WriteLine("MdiParent is null");
                    MessageBox.Show("MdiParent is null. Cannot set parent for AddRole form.", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                
                addRoleForm.MdiParent = this.MdiParent;
                Debug.WriteLine($"MdiParent set: {addRoleForm.MdiParent != null}");
                
                addRoleForm.FormClosed += (s, args) => {
                    // Refresh roles list when AddRole form is closed
                    Debug.WriteLine("AddRole form closed, refreshing roles");
                    LoadRoles();
                };
                
                Debug.WriteLine("About to show AddRole form");
                addRoleForm.Show();
                Debug.WriteLine("AddRole form shown");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in BtnAddRole_Click: {ex.Message}\n{ex.StackTrace}");
                MessageBox.Show($"Error opening Add Role form: {ex.Message}\n\nStack Trace: {ex.StackTrace}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle Role Edit button click
        /// </summary>
        private void BtnRoleEdit_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentRoleId <= 0)
                {
                    MessageBox.Show("Please select a role first.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Enable grid for editing
                gridViewRolePermissions.OptionsBehavior.Editable = true;
                btnRoleEdit.Enabled = false;

                MessageBox.Show("Role permissions grid is now editable. Make your changes and click Save.", "Edit Mode",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error enabling edit mode: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle Role Load button click
        /// </summary>
        private void BtnRoleLoad_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentRoleId <= 0)
                {
                    MessageBox.Show("Please select a role first.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                LoadRolePermissions();
                gridViewRolePermissions.OptionsBehavior.Editable = false;
                btnRoleEdit.Enabled = true;
                btnDeleteRole.Enabled = true;
                
                // Removed success message box
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading role permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle Delete Role button click
        /// </summary>
        private void BtnDeleteRole_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentRoleId <= 0)
                {
                    MessageBox.Show("Please select a role to delete.", "No Role Selected",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                string roleName = cmbRoles.Text;

                // Check if role is assigned to users
                int userCount = PermissionService.GetRoleUsageCount(_currentRoleId);

                if (userCount < 0)
                {
                    MessageBox.Show("Error checking role usage. Please try again.", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                if (userCount > 0)
                {
                    MessageBox.Show($"Cannot delete role '{roleName}'. {userCount} user(s) are assigned to this role.\n\n" +
                        "Please reassign users to different roles before deleting.",
                        "Role In Use", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Confirm deletion
                var result = MessageBox.Show($"Are you sure you want to delete role '{roleName}'?\n\n" +
                    "This action will permanently remove the role and all its permissions.",
                    "Confirm Role Deletion", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    if (PermissionService.DeleteRole(_currentRoleId))
                    {
                        MessageBox.Show($"Role '{roleName}' has been successfully deleted.",
                            "Role Deleted", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // Refresh and reset form
                        LoadRoles();
                        ClearRolePermissionsGrid();
                        _currentRoleId = 0;
                        btnRoleEdit.Enabled = false;
                        btnDeleteRole.Enabled = false;
                    }
                    else
                    {
                        MessageBox.Show("Failed to delete role. Please try again.",
                            "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error deleting role: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle User Edit button click
        /// </summary>
        private void BtnUserEdit_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentUserId <= 0)
                {
                    MessageBox.Show("Please select a user first.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Enable grid for editing
                gridViewUserPermissions.OptionsBehavior.Editable = true;
                btnUserEdit.Enabled = false;

                MessageBox.Show("User permissions grid is now editable. Make your changes and click Save.", "Edit Mode",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error enabling edit mode: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle User Load button click
        /// </summary>
        private void BtnUserLoad_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentUserId <= 0)
                {
                    MessageBox.Show("Please select a user first.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                LoadUserPermissions();
                gridViewUserPermissions.OptionsBehavior.Editable = false;
                btnUserEdit.Enabled = true;

                MessageBox.Show("User permissions loaded successfully.", "Success",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading user permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle Global Edit button click
        /// </summary>
        private void BtnGlobalEdit_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentUserId <= 0)
                {
                    MessageBox.Show("Please select a user first.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Enable checkboxes for editing by making them not read-only
                chkCanCreateNewEntry.Properties.ReadOnly = false;
                chkCanEditUsers.Properties.ReadOnly = false;
                chkCanDeleteUsers.Properties.ReadOnly = false;
                chkCanPrintUsers.Properties.ReadOnly = false;
                btnGlobalEdit.Enabled = false;

                MessageBox.Show("Global permissions are now editable. Make your changes and click Save.", "Edit Mode",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error enabling edit mode: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle Global Load button click
        /// </summary>
        private void BtnGlobalLoad_Click(object sender, EventArgs e)
        {
            try
            {
                if (_currentUserId <= 0)
                {
                    MessageBox.Show("Please select a user first.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                LoadGlobalPermissions();
                chkCanCreateNewEntry.Properties.ReadOnly = true;
                chkCanEditUsers.Properties.ReadOnly = true;
                chkCanDeleteUsers.Properties.ReadOnly = true;
                chkCanPrintUsers.Properties.ReadOnly = true;
                btnGlobalEdit.Enabled = true;

                MessageBox.Show("Global permissions loaded successfully.", "Success",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading global permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Update button states based on current form state
        /// </summary>
        private void UpdateButtonStates()
        {
            btnSave.Enabled = _hasUnsavedChanges;
            btnCancel.Enabled = _hasUnsavedChanges;

            // Update Edit button states based on selection
            btnRoleEdit.Enabled = _currentRoleId > 0 && !gridViewRolePermissions.OptionsBehavior.Editable;
            btnUserEdit.Enabled = _currentUserId > 0 && !gridViewUserPermissions.OptionsBehavior.Editable;
            btnGlobalEdit.Enabled = _currentUserId > 0 && !chkCanCreateNewEntry.Enabled;
        }

        /// <summary>
        /// Recalculate effective permissions for a specific row
        /// </summary>
        private void RecalculateEffectivePermissions(int rowHandle)
        {
            try
            {
                var gridView = gridViewUserPermissions;

                // Get role and user permissions for this row
                var roleRead = (bool)gridView.GetRowCellValue(rowHandle, "RoleRead");
                var roleNew = (bool)gridView.GetRowCellValue(rowHandle, "RoleNew");
                var roleEdit = (bool)gridView.GetRowCellValue(rowHandle, "RoleEdit");
                var roleDelete = (bool)gridView.GetRowCellValue(rowHandle, "RoleDelete");
                var rolePrint = (bool)gridView.GetRowCellValue(rowHandle, "RolePrint");

                var userRead = gridView.GetRowCellValue(rowHandle, "UserRead") as bool?;
                var userNew = gridView.GetRowCellValue(rowHandle, "UserNew") as bool?;
                var userEdit = gridView.GetRowCellValue(rowHandle, "UserEdit") as bool?;
                var userDelete = gridView.GetRowCellValue(rowHandle, "UserDelete") as bool?;
                var userPrint = gridView.GetRowCellValue(rowHandle, "UserPrint") as bool?;

                // Calculate effective permissions (user override takes precedence)
                gridView.SetRowCellValue(rowHandle, "EffectiveRead", userRead ?? roleRead);
                gridView.SetRowCellValue(rowHandle, "EffectiveNew", userNew ?? roleNew);
                gridView.SetRowCellValue(rowHandle, "EffectiveEdit", userEdit ?? roleEdit);
                gridView.SetRowCellValue(rowHandle, "EffectiveDelete", userDelete ?? roleDelete);
                gridView.SetRowCellValue(rowHandle, "EffectivePrint", userPrint ?? rolePrint);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error recalculating effective permissions: {ex.Message}");
            }
        }

        /// <summary>
        /// Save all changes based on current tab
        /// </summary>
        private bool SaveChanges()
        {
            try
            {
                if (tabControlMain.SelectedTabPage == tabPageRolePermissions)
                {
                    return SaveRolePermissions();
                }
                else if (tabControlMain.SelectedTabPage == tabPageUserPermissions)
                {
                    return SaveUserPermissions();
                }
                else if (tabControlMain.SelectedTabPage == tabPageGlobalPermissions)
                {
                    return SaveGlobalPermissions();
                }

                return false;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving changes: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Save role permissions from grid
        /// </summary>
        private bool SaveRolePermissions()
        {
            if (_currentRoleId <= 0) return false;

            try
            {
                var permissions = new List<RolePermissionUpdate>();
                var gridView = gridViewRolePermissions;

                for (int i = 0; i < gridView.RowCount; i++)
                {
                    var formName = gridView.GetRowCellValue(i, "FormName")?.ToString();
                    if (string.IsNullOrEmpty(formName)) continue;

                    var permission = new RolePermissionUpdate
                    {
                        RoleId = _currentRoleId,
                        FormName = formName,
                        ReadPermission = (bool)gridView.GetRowCellValue(i, "ReadPermission"),
                        NewPermission = (bool)gridView.GetRowCellValue(i, "NewPermission"),
                        EditPermission = (bool)gridView.GetRowCellValue(i, "EditPermission"),
                        DeletePermission = (bool)gridView.GetRowCellValue(i, "DeletePermission"),
                        PrintPermission = (bool)gridView.GetRowCellValue(i, "PrintPermission")
                    };

                    permissions.Add(permission);
                }

                PermissionService.UpdateRolePermissions(_currentRoleId, permissions);
                PermissionService.ClearCache();

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving role permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Save user permissions from grid
        /// </summary>
        private bool SaveUserPermissions()
        {
            if (_currentUserId <= 0) return false;

            try
            {
                var permissions = new List<UserPermissionUpdate>();
                var gridView = gridViewUserPermissions;

                for (int i = 0; i < gridView.RowCount; i++)
                {
                    var formName = gridView.GetRowCellValue(i, "FormName")?.ToString();
                    if (string.IsNullOrEmpty(formName)) continue;

                    // Get the WritePermission value for both New and Edit permissions
                    var writePermission = gridView.GetRowCellValue(i, "WritePermission") as bool?;

                    var permission = new UserPermissionUpdate
                    {
                        UserId = _currentUserId,
                        FormName = formName,
                        ReadPermission = gridView.GetRowCellValue(i, "ReadPermission") as bool?,
                        NewPermission = writePermission, // WritePermission maps to both New and Edit
                        EditPermission = writePermission,
                        DeletePermission = gridView.GetRowCellValue(i, "DeletePermission") as bool?,
                        PrintPermission = gridView.GetRowCellValue(i, "PrintPermission") as bool?
                    };

                    permissions.Add(permission);
                }

                PermissionService.UpdateUserPermissions(_currentUserId, permissions);
                PermissionService.ClearCache();

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving user permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Save global permissions from checkboxes
        /// </summary>
        private bool SaveGlobalPermissions()
        {
            if (_currentUserId <= 0) return false;

            try
            {
                var globalPermissions = new GlobalPermissionModel
                {
                    UserId = _currentUserId,
                    CanReadUsers = true, // Always allow read for existing users
                    CanCreateUsers = chkCanCreateNewEntry.Checked,
                    CanEditUsers = chkCanEditUsers.Checked,
                    CanDeleteUsers = chkCanDeleteUsers.Checked,
                    CanPrintUsers = chkCanPrintUsers.Checked
                };

                bool success = GlobalPermissionService.UpdateGlobalPermissions(_currentUserId, globalPermissions);

                if (success)
                {
                    Debug.WriteLine($"Global permissions saved for user {_currentUserId}");
                }

                return success;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving global permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }



        /// <summary>
        /// Handle form closing to check for unsaved changes
        /// </summary>
        protected override void OnFormClosing(FormClosingEventArgs e)
        {
            if (_hasUnsavedChanges)
            {
                var result = MessageBox.Show("You have unsaved changes. Do you want to save before closing?",
                    "Unsaved Changes", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    if (!SaveChanges())
                    {
                        e.Cancel = true;
                        return;
                    }
                }
                else if (result == DialogResult.Cancel)
                {
                    e.Cancel = true;
                    return;
                }
            }

            base.OnFormClosing(e);
        }

        /// <summary>
        /// Synchronizes user selection between different parts of the form
        /// </summary>
        /// <param name="userId">User ID to synchronize</param>
        private void SynchronizeUserSelection(int userId)
        {
            // This method synchronizes user selection across different controls
            // Implementation depends on specific UI control names and structure
            // Currently implemented as placeholder to avoid compilation errors
        }

        /// <summary>
        /// Clear role permissions grid
        /// </summary>
        private void ClearRolePermissionsGrid()
        {
            try
            {
                gridControlRolePermissions.DataSource = null;
                gridViewRolePermissions.OptionsBehavior.Editable = false;
                btnRoleEdit.Enabled = false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error clearing role permissions grid: {ex.Message}");
            }
        }

        /// <summary>
        /// Clear user permissions grid
        /// </summary>
        private void ClearUserPermissionsGrid()
        {
            try
            {
                gridControlUserPermissions.DataSource = null;
                gridViewUserPermissions.OptionsBehavior.Editable = false;
                btnUserEdit.Enabled = false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error clearing user permissions grid: {ex.Message}");
            }
        }

        /// <summary>
        /// Clear global permissions checkboxes
        /// </summary>
        private void ClearGlobalPermissions()
        {
            try
            {
                chkCanCreateNewEntry.Checked = false;
                chkCanEditUsers.Checked = false;
                chkCanDeleteUsers.Checked = false;
                chkCanPrintUsers.Checked = false;

                chkCanCreateNewEntry.Enabled = false;
                chkCanEditUsers.Enabled = false;
                chkCanDeleteUsers.Enabled = false;
                chkCanPrintUsers.Enabled = false;

                btnGlobalEdit.Enabled = false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error clearing global permissions: {ex.Message}");
            }
        }

        /// <summary>
        /// Reset the entire form to initial state
        /// </summary>
        private void ResetForm()
        {
            try
            {
                _isLoading = true;

                // Reset role tab
                _currentRoleId = 0;
                cmbRoles.SelectedIndex = -1;
                ClearRolePermissionsGrid();

                // Reset user tab
                _currentUserId = 0;
                cmbUsers.SelectedIndex = -1;
                ClearUserPermissionsGrid();
                ClearGlobalPermissions();

                // Reset state
                _hasUnsavedChanges = false;
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error resetting form: {ex.Message}");
            }
            finally
            {
                _isLoading = false;
            }
        }

        #endregion

        private void chkCanCreateNewEntry_CheckedChanged(object sender, EventArgs e)
        {

        }
    }
}
