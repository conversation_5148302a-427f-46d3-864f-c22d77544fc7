-- GetUser.sql
-- Contains queries for retrieving user information

-- [GetUserByUsername] --
SELECT
    user_id,
    username,
    password_hash,
    password_salt,
    full_name,
    email,
    role,
    is_active,
    last_login_date,
    created_date,
    department,
    phone,
    designation,
    short_name,
    photo_path,
    edit_password,
    can_read,
    can_create,
    can_edit,
    can_delete,
    can_print
FROM
    users
WHERE
    username = @username;
-- [End] --

-- [GetUserById] --
SELECT
    user_id,
    username,
    password_hash,
    password_salt,
    full_name,
    email,
    role,
    is_active,
    last_login_date,
    created_date,
    department,
    phone,
    designation,
    short_name,
    photo_path,
    edit_password,
    can_read,
    can_create,
    can_edit,
    can_delete,
    can_print
FROM
    users
WHERE
    user_id = @user_id;
-- [End] --

-- [GetAllUsers] --
SELECT
    user_id,
    username,
    full_name,
    email,
    role,
    department,
    phone,
    designation,
    short_name,
    is_active,
    last_login_date,
    created_date
FROM
    users
ORDER BY
    full_name ASC;
-- [End] --

-- [GetActiveUsers] --
SELECT
    user_id,
    username,
    full_name,
    email,
    role,
    department,
    phone,
    designation,
    short_name,
    is_active,
    last_login_date,
    created_date
FROM
    users
WHERE
    is_active = TRUE
ORDER BY
    full_name ASC;
-- [End] --

-- [SearchUsers] --
SELECT
    user_id,
    username,
    full_name,
    email,
    role,
    department,
    phone,
    designation,
    short_name,
    is_active,
    last_login_date,
    created_date
FROM
    users
WHERE
    (full_name ILIKE '%' || @search_term || '%'
     OR username ILIKE '%' || @search_term || '%'
     OR email ILIKE '%' || @search_term || '%'
     OR role ILIKE '%' || @search_term || '%'
     OR department ILIKE '%' || @search_term || '%'
     OR designation ILIKE '%' || @search_term || '%')
ORDER BY
    full_name ASC;
-- [End] --
